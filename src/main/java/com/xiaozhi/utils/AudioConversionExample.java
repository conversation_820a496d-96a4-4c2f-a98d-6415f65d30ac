package com.xiaozhi.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;

/**
 * 音频转换示例类
 * 演示如何使用 AudioUtils.convertToStereoMp3 方法
 */
@Slf4j
@Component
public class AudioConversionExample {

    /**
     * 将单声道音频文件转换为双声道MP3
     * 
     * @param inputFilePath 输入音频文件路径
     * @return 转换后的双声道MP3文件路径，如果转换失败返回null
     */
    public String convertMonoToStereo(String inputFilePath) {
        try {
            // 检查输入文件是否存在
            if (!Files.exists(Paths.get(inputFilePath))) {
                log.error("输入文件不存在: {}", inputFilePath);
                return null;
            }

            log.info("开始转换音频文件: {}", inputFilePath);
            
            // 调用AudioUtils的转换方法
            String stereoFilePath = AudioUtils.monoToStereo(inputFilePath);
            
            if (stereoFilePath != null) {
                log.info("音频转换成功: {} -> {}", inputFilePath, stereoFilePath);
                
                // 可选：删除原始单声道文件以节省空间
                // Files.deleteIfExists(Paths.get(inputFilePath));
                // log.info("已删除原始文件: {}", inputFilePath);
                
                return stereoFilePath;
            } else {
                log.error("音频转换失败: {}", inputFilePath);
                return null;
            }
            
        } catch (IOException e) {
            log.error("音频转换过程中发生错误: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 批量转换音频文件为双声道MP3
     * 
     * @param inputFilePaths 输入音频文件路径数组
     * @return 转换成功的文件数量
     */
    public int batchConvertToStereo(String... inputFilePaths) {
        int successCount = 0;
        
        for (String inputPath : inputFilePaths) {
            String result = convertMonoToStereo(inputPath);
            if (result != null) {
                successCount++;
            }
        }
        
        log.info("批量转换完成，成功转换 {} / {} 个文件", successCount, inputFilePaths.length);
        return successCount;
    }
}
