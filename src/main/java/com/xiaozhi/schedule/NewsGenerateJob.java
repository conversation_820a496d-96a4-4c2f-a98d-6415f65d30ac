package com.xiaozhi.schedule;

import com.xiaozhi.service.MediaService;
import com.xiaozhi.vo.MediaGenerationParams;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class NewsGenerateJob {

    @Resource
    private MediaService mediaService;

    //@Scheduled(cron = "0 0 6 * * ?")
    //@Scheduled(fixedDelay = 20 * 1000)
    public void subscribe() {
        mediaService.generate(new MediaGenerationParams().setCategory("news").setTitle("生成今日资讯"));
    }
}
