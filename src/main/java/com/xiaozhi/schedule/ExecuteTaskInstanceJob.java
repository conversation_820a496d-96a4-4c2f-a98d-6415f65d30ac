package com.xiaozhi.schedule;

import com.xiaozhi.common.web.OhMyLambdaQueryWrapper;
import com.xiaozhi.communication.common.SessionManager;
import com.xiaozhi.communication.domain.TaskChain;
import com.xiaozhi.communication.server.mqtt.MqttServerPublish;
import com.xiaozhi.dao.ManagerMapper;
import com.xiaozhi.dao.TaskInstanceMapper;
import com.xiaozhi.dialogue.llm.ChatService;
import com.xiaozhi.dialogue.service.DialogueService;
import com.xiaozhi.dialogue.tts.factory.TtsServiceFactory;
import com.xiaozhi.entity.Manager;
import com.xiaozhi.enums.TaskContent;
import com.xiaozhi.enums.TaskStatus;
import com.xiaozhi.utils.OpusProcessor;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
@Component
public class ExecuteTaskInstanceJob {

    @Resource
    private ManagerMapper managerMapper;

    @Resource
    private TaskInstanceMapper taskInstanceMapper;

    @Resource
    private ChatService chatService;

    @Resource
    private SessionManager sessionManager;

    @Resource
    private DialogueService dialogueService;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private MqttServerPublish mqttServerPublish;

    @Resource
    private TtsServiceFactory ttsServiceFactory;

    @Resource
    private OpusProcessor opusProcessor;

    private final String TaskDelayQueue = "xiaozhi:task:queue";

    // Lua 脚本：原子地取出并删除 0-now 之间的数据
    private final String ZRangeScript = """
            local msgs = redis.call('ZRANGEBYSCORE', KEYS[1], 0, ARGV[1], 'limit', 0, 100)
            if (#msgs > 0) then
            redis.call('ZREM', KEYS[1], unpack(msgs))
            return msgs
            else return {} end
            """;

    //@Scheduled(fixedRate = 20 * 1000)
    public void test() throws InterruptedException {
        var deviceId = "74:4d:bd:7f:2f:98";
        var topic = STR."devices/p2p/GID@@@\{deviceId.replaceAll(":", "_")}";

        mqttServerPublish.wakeup(topic);

        TimeUnit.SECONDS.sleep(2);

        var newSession = sessionManager.getSessionByDeviceId(deviceId);

        var ttsService = ttsServiceFactory.getTtsService(newSession.getCurrentRole().getTtsConfig(), newSession.getCurrentRole().getVoice());

        var text = "Hi, what are you doing?";
        newSession.sendTTSStart();
        newSession.sendSentenceStart(text);
        ttsService.streamTextToSpeech(text)
                .asFlux()
                .map(it -> opusProcessor.pcmToOpus(newSession.getSessionId(), it, true))
                .subscribe(bytes -> {
                    log.info("opus bytes {}", bytes.size());

                    for (var b : bytes) {
                        newSession.sendBinaryMessage(b);
                    }

                    //newSession.sendBinaryMessage(bytes);

                }, Throwable::printStackTrace, newSession::sendTTSStop);
    }

    @Scheduled(cron = "0 */1 * * * ?")
    public void run() {
        var taskJsonArray = Optional.ofNullable(stringRedisTemplate.opsForZSet().rangeByScore(TaskDelayQueue, 0, 1757329800000L))
                .orElseGet(Set::of);

        log.info("Tasks {}", taskJsonArray);

        var tasks = taskJsonArray.stream()
                .map(Integer::valueOf)
                .toList();

        for (var taskId : tasks) {
            var instance = taskInstanceMapper.selectById(taskId);
            if (instance == null) continue;
            if (instance.getStatus() != TaskStatus.Waiting) continue;

            var now = Instant.now();

            // create task chain
            var durations = new ArrayList<Integer>();
            for (int i = 0; i < instance.getDuration().length(); i += 2) {
                durations.add(Integer.parseInt(instance.getDuration().substring(i, i + 2)));
            }

            var idx = new AtomicInteger(0);
            var dummy = new TaskChain();
            Arrays.stream(TaskContent.values())
                    .filter(it -> (it.getValue() & instance.getContent()) != 0)
                    .map(it -> new TaskChain().setInstanceId(instance.getId()).setContent(it).setStartTime(now).setDuration(durations.get(idx.getAndIncrement())))
                    .reduce(dummy, (z, x) -> {
                        z.setNext(x);
                        return x;
                    }, (_, it) -> it);

            var taskChain = dummy.getNext();
            var topic = STR."devices/p2p/GID@@@\{instance.getDeviceId().replaceAll(":", "_")}";

            var session = sessionManager.getSessionByDeviceId(instance.getDeviceId());

            switch (instance.getType()) {
                case Listening, Information -> {
                    mqttServerPublish.open(topic, "player", "news");
                    sessionManager.putListeningStopTime(instance.getDeviceId(), Instant.now().plusSeconds(taskChain.getDuration() * 60));
                }
                case Bedtime, Conversation -> {
                    // wakeup
                    if (session == null || !session.isOpen()) {
                        mqttServerPublish.wakeup(topic);
                    }
                    Thread.startVirtualThread(() -> {
                        try {
                            TimeUnit.SECONDS.sleep(2);
                        } catch (InterruptedException e) {
                            throw new RuntimeException(e);
                        }
                        var newSession = sessionManager.getSessionByDeviceId(instance.getDeviceId());
                        if (newSession == null) return;
                        newSession.setTaskChain(taskChain);

                        if (newSession.isPlaying()) {
                            // 设备正在播放，将任务置为取消
                            instance.setStatus(TaskStatus.Canceled);
                            taskInstanceMapper.updateById(instance);
                            return;
                        }

                        var managerQuery = new OhMyLambdaQueryWrapper<Manager>()
                                .eq(Manager::getId, instance.getManagerId())
                                .select(Manager::getId, Manager::getName, Manager::getCefr);
                        var manager = managerMapper.selectOne(managerQuery);

                        var hello = chatService.getStartSentence(manager.getName(), manager.getCefr(), "【】");
                        dialogueService.sendSentenceWithoutSegmentation(newSession, hello, true)
                                .thenRun(() -> {
                                    instance.setStatus(TaskStatus.Running);
                                    taskInstanceMapper.updateById(instance);
                                });
                    });

                }
            }

        }
    }
}
