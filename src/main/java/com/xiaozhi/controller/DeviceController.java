package com.xiaozhi.controller;

import com.xiaozhi.common.interceptor.Authorized;
import com.xiaozhi.common.interceptor.PassAuth;
import com.xiaozhi.common.interceptor.QueryParam;
import com.xiaozhi.common.web.AuthorizedUser;
import com.xiaozhi.common.web.BizError;
import com.xiaozhi.common.web.Resp;
import com.xiaozhi.communication.common.SessionManager;
import com.xiaozhi.communication.server.mqtt.MqttServerPublish;
import com.xiaozhi.dialogue.service.DialogueService;
import com.xiaozhi.entity.SysDevice;
import com.xiaozhi.service.SysDeviceService;
import com.xiaozhi.vo.DeviceCreateParams;
import com.xiaozhi.vo.DeviceQueryParams;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.vavr.control.Either;
import jakarta.annotation.Resource;

import org.springframework.web.bind.annotation.*;

import java.util.concurrent.TimeUnit;

@RestController
@RequestMapping("/devices")
@Tag(name = "设备管理", description = "设备管理")
public class DeviceController extends BaseController {

    @Resource
    private SysDeviceService deviceService;

    @Resource
    private MqttServerPublish mqttServerPublish;

    @Resource
    private DialogueService dialogueService;

    @Resource
    private SessionManager sessionManager;

    @GetMapping()
    @Operation(summary = "设备列表")
    public Resp find(@Authorized AuthorizedUser user, @QueryParam DeviceQueryParams params) {
        return deviceService.findPage(params, user.getId());
    }

    @PostMapping()
    @Operation(summary = "添加设备")
    public Either<BizError, ?> add(@Authorized AuthorizedUser user, @RequestBody DeviceCreateParams params) {
        params.setUserId(user.getId());
        return deviceService.add(params);
    }

    @PutMapping("/{id}")
    @Operation(summary = "更新设备")
    public Either<BizError, ?> update(@PathVariable Integer id, @RequestBody SysDevice device) {
        return deviceService.update(id, device);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除设备")
    public Either<BizError, ?> delete(@PathVariable Integer id) {
        return deviceService.delete(id);
    }

    @PassAuth
    @GetMapping("/wakeup")
    @Operation(summary = "")
    public Resp test(@RequestParam(name = "device_id") String deviceId) throws InterruptedException {
        var topic = STR."devices/p2p/GID@@@\{deviceId.replaceAll(":", "_")}";
        mqttServerPublish.wakeup(topic);
        TimeUnit.SECONDS.sleep(2);
        mqttServerPublish.open(topic, "player", "news");
        // var session = sessionManager.getSessionByDeviceId(deviceId);
        mqttServerPublish.close(topic, "player");
        // dialogueService.sendSentenceWithoutSegmentation(session, "hello from server push", false);
        return Resp.succeed(true);
    }

}