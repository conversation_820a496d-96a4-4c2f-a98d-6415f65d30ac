package com.xiaozhi.dialogue.llm.memory;

import com.fasterxml.jackson.core.type.TypeReference;
import com.xiaozhi.dialogue.llm.ChatModelService;
import com.xiaozhi.dialogue.llm.Prompts;
import com.xiaozhi.entity.SysDevice;
import com.xiaozhi.entity.SysMessage;
import com.xiaozhi.entity.SysRole;
import com.xiaozhi.utils.JsonUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.messages.AssistantMessage;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.SystemMessage;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.prompt.PromptTemplate;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class SummaryConversation extends Conversation {

    private int remain;

    private final Set<String> facts;

    private final ChatMemory chatMemory;

    private final ChatModelService chatModel;

    private final int maxMessages;

    public SummaryConversation(SysDevice device, SysRole role, String sessionId, ChatMemory chatMemory, ChatModelService chatModel, int maxMessages) {
        super(device, role, sessionId);
        this.chatModel = chatModel;
        this.chatMemory = chatMemory;
        facts = new LinkedHashSet<>();
        this.maxMessages = maxMessages;
        this.remain = maxMessages;
    }

    public static class Builder {
        private SysDevice device;
        private SysRole role;
        private String sessionId;
        private int maxMessages;
        private ChatMemory chatMemory;
        private ChatModelService chatModel;

        public Builder device(SysDevice device) {
            this.device = device;
            return this;
        }

        public Builder role(SysRole role) {
            this.role = role;
            return this;
        }

        public Builder sessionId(String sessionId) {
            this.sessionId = sessionId;
            return this;
        }

        public Builder chatMemory(ChatMemory chatMemory) {
            this.chatMemory = chatMemory;
            return this;
        }

        public Builder maxMessages(int maxMessages) {
            this.maxMessages = maxMessages;
            return this;
        }

        public Builder chatModel(ChatModelService chatModel) {
            this.chatModel = chatModel;
            return this;
        }

        public SummaryConversation build() {
            return new SummaryConversation(device, role, sessionId, chatMemory, chatModel, maxMessages);
        }
    }

    public static Builder builder() {
        return new Builder();
    }

    @Override
    public void addMessage(UserMessage userMessage, Long userTimeMillis, AssistantMessage assistantMessage, Long assistantTimeMillis) {
        super.addMessage(userMessage, userTimeMillis, assistantMessage, assistantTimeMillis);

        var roleId = role().getId();
        var deviceId = device().getDeviceId();
        var messageType = SysMessage.MESSAGE_TYPE_NORMAL;

        if (userMessage != null) {
            chatMemory.addMessage(deviceId, sessionId(), userMessage.getMessageType().getValue(), userMessage.getText(),
                    roleId, messageType, userTimeMillis);
        }
        if (assistantMessage != null) {
            chatMemory.addMessage(deviceId, sessionId(), assistantMessage.getMessageType().getValue(), assistantMessage.getText(),
                    roleId, messageType, assistantTimeMillis);
        }

        // 更新当前剩余的旧消息
        remain -= 2;

        // 移除最旧的消息
        while (messages.size() > maxMessages) {
            messages.removeFirst();
        }

        if (remain == 0) {
            var input = messages.stream()
                    .map(msg -> switch (msg) {
                        case UserMessage m -> STR."user: \{m.getText()}";
                        case AssistantMessage m -> STR."assistant: \{m.getText()}";
                        default -> "";
                    })
                    .collect(Collectors.joining("\n"));
            log.info("Fact input {}", input);

            var response = chatModel.call(
                    new SystemMessage(Prompts.FACT_RETRIEVAL_PROMPT),
                    new UserMessage(STR."Input:\n\{input}")
            );

            log.info("FACTS {}", response);

            JsonUtil.parse(response, FactResult.class).onSuccess(res -> facts.addAll(res.facts));

            chatMemory.addMessage(device().getDeviceId(), sessionId(), "summary", JsonUtil.toJson(facts), role().getId(), "summary", assistantTimeMillis);

            remain += maxMessages;
        }
    }

    @Override
    public List<Message> prompt(UserMessage userMessage) {
        var preferences = facts.stream()
                .map(it -> STR."- \{it}")
                .collect(Collectors.joining("\n"));
        var intro = STR."""
                \{role().getIntro()}
                Here are the user preferences:
                \{preferences}
                """;
        var systemMessage = new SystemMessage(intro);

        var messages = new ArrayList<Message>();
        messages.add(systemMessage);
        messages.addAll(this.messages);
        messages.add(userMessage);

        messages.forEach(m -> log.info("Msg =======> {}: {}", m.getMessageType(), m.getText()));

        return messages;
    }

    @Override
    public void loadMemory(Map<String, Object> vars) {
        super.loadMemory(vars);

        // load summary
        var summaries = chatMemory.getMessages(device().getDeviceId(), "summary", 1);
        if (summaries.size() == 1) {
            var memory = summaries.getFirst();
            JsonUtil.parse(memory.getContent(), new TypeReference<List<String>>() {
            }).onSuccess(this.facts::addAll);
        }

        // load history messages
        var history = chatMemory.getMessages(device().getDeviceId(), SysMessage.MESSAGE_TYPE_NORMAL, maxMessages);
        this.messages.addAll(convert(history));

        // apply template vars
        var systemPrompt = new PromptTemplate(role().getIntro()).create(vars);
        role().setIntro(systemPrompt.getContents());
    }

    @Data
    public static class FactResult {
        private List<String> facts;
    }
}
