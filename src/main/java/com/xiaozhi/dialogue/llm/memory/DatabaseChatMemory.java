package com.xiaozhi.dialogue.llm.memory;

import com.xiaozhi.entity.BaseEntity;
import com.xiaozhi.entity.SysMessage;
import com.xiaozhi.service.SysMessageService;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.ZoneId;
import java.util.Comparator;
import java.util.List;

/**
 * 基于数据库的聊天记忆实现
 * 全局单例类，负责Conversatin里消息的获取、保存、清理。
 * 后续考虑：DatabaseChatMemory 是对 SysMessageService 的一层薄封装，未来或者有可能考虑合并这两者。
 */
@Slf4j
@Service
@ConditionalOnProperty(name = "xiaozhi.memory.type", havingValue = "database")
public class DatabaseChatMemory implements ChatMemory {

    private final SysMessageService messageService;

    @Autowired
    public DatabaseChatMemory(SysMessageService messageService) {
        this.messageService = messageService;
    }

    @Override
    public void addMessage(String deviceId, String sessionId, String sender, String content, Integer roleId, String messageType, Long timeMillis) {
        try {
            SysMessage message = new SysMessage();
            message.setDeviceId(deviceId);
            message.setSessionId(sessionId);
            message.setSender(sender);
            message.setContent(content);
            message.setRoleId(roleId);
            message.setType(messageType);
            message.setCreatedAt(Instant.ofEpochMilli(timeMillis).atZone(ZoneId.systemDefault()).toLocalDateTime());
            messageService.save(message);
        } catch (Exception e) {
            log.error("保存消息时出错: {}", e.getMessage(), e);
        }
    }

    @Override
    public List<SysMessage> getMessages(String deviceId, String messageType, Integer limit) {
        var messages = messageService.findOf(deviceId, messageType, limit);
        messages.sort(Comparator.comparing(BaseEntity::getCreatedAt));
        return messages;
    }

    @Override
    public void clearMessages(String deviceId) {
        try {
            // 清除设备的历史消息
            SysMessage deleteMessage = new SysMessage();
            deleteMessage.setDeviceId(deviceId);
            // messageService.update(deleteMessage);
        } catch (Exception e) {
            log.error("清除设备历史记录时出错: {}", e.getMessage(), e);
        }
    }

}