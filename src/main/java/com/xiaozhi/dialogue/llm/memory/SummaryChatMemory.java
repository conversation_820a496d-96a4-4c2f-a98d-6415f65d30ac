package com.xiaozhi.dialogue.llm.memory;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.xiaozhi.common.web.OhMyLambdaQueryWrapper;
import com.xiaozhi.dao.ChatMemoryMapper;
import com.xiaozhi.entity.BaseEntity;
import com.xiaozhi.entity.SysChatMemory;
import com.xiaozhi.entity.SysMessage;
import com.xiaozhi.service.SysMessageService;
import com.xiaozhi.utils.JsonUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.ZoneId;
import java.util.*;

/**
 * 基于OpenMemory的聊天记忆实现
 * 使用OpenMemory API进行智能记忆管理和摘要生成
 * 只有在配置启用时才会激活此实现
 */
@Slf4j
@Service
@ConditionalOnProperty(name = "xiaozhi.memory.type", havingValue = "summary")
public class SummaryChatMemory implements ChatMemory {

    @Resource
    private ChatMemoryMapper chatMemoryMapper;

    @Resource
    private SysMessageService messageService;

    @Override
    public void addMessage(String deviceId, String sessionId, String sender, String content, Integer roleId, String messageType, Long timeMillis) {
        if (messageType.equals("summary")) {
            var updateQuery = new LambdaUpdateWrapper<SysChatMemory>()
                    .eq(SysChatMemory::getDeviceId, deviceId)
                    .set(SysChatMemory::getFacts, content);
            chatMemoryMapper.update(updateQuery);
        } else {
            var message = new SysMessage();
            message.setDeviceId(deviceId);
            message.setSessionId(sessionId);
            message.setSender(sender);
            message.setContent(content);
            message.setRoleId(roleId);
            message.setType(messageType);
            message.setCreatedAt(Instant.ofEpochMilli(timeMillis).atZone(ZoneId.systemDefault()).toLocalDateTime());
            messageService.save(message);
        }
    }

    @Override
    public List<SysMessage> getMessages(String deviceId, String messageType, Integer limit) {
        if (messageType.equals("summary")) {
            var query = new OhMyLambdaQueryWrapper<SysChatMemory>()
                    .eq(SysChatMemory::getDeviceId, deviceId);

            var memory = chatMemoryMapper.selectOne(query);

            if (memory == null) {
                memory = new SysChatMemory()
                        .setFacts("[]")
                        .setDeviceId(deviceId);
                chatMemoryMapper.insert(memory);
            }

            return List.of(new SysMessage()
                    .setContent(memory.getFacts())
                    .setDeviceId(deviceId)
                    .setSender("summary")
                    .setType("summary"));
        }
        var messages = messageService.findOf(deviceId, messageType, limit);
        messages.sort(Comparator.comparing(BaseEntity::getCreatedAt));
        return messages;
    }

    @Override
    public void clearMessages(String deviceId) {

    }


}
