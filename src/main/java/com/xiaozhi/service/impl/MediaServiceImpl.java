package com.xiaozhi.service.impl;

import com.xiaozhi.common.web.BizError;
import com.xiaozhi.common.web.OhMyLambdaQueryWrapper;
import com.xiaozhi.common.web.OhMyQueryWrapper;
import com.xiaozhi.common.web.Resp;
import com.xiaozhi.dao.MediaMapper;
import com.xiaozhi.dto.MediaInfo;
import com.xiaozhi.entity.Media;
import com.xiaozhi.service.FileResourceService;
import com.xiaozhi.service.MediaService;
import com.xiaozhi.utils.AudioUtils;
import com.xiaozhi.utils.AzureSpeechUtil;
import com.xiaozhi.utils.CozeUtil;
import com.xiaozhi.utils.ImageUtil;
import com.xiaozhi.vo.MediaCreateParams;
import com.xiaozhi.vo.MediaGenerationParams;
import com.xiaozhi.vo.MediaQueryParams;
import io.vavr.control.Either;
import io.vavr.control.Option;
import io.vavr.control.Try;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.io.FileInputStream;
import java.net.URI;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Stream;

@Service
public class MediaServiceImpl implements MediaService {

    @Resource
    private MediaMapper mediaMapper;

    @Resource
    private FileResourceService fileResourceService;

    private final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy年MM月dd日");

    public Resp _find4device(MediaQueryParams params) {
        var storyQuery = new OhMyQueryWrapper<MediaInfo>()
                .eq("m.category", "story")
                .eq("m.is_deleted", false);
        var newsQuery = new OhMyQueryWrapper<MediaInfo>()
                .eq("m.category", "news")
                .eq("m.is_deleted", false);

        params.setLimit(params.getLimit() / 2);
        var story = mediaMapper.findPage(params.toPage(), storyQuery);
        var news = mediaMapper.findPage(params.toPage(), newsQuery);

        var records = Stream.concat(story.getRecords().stream(), news.getRecords().stream()).toList();

        return Resp.of(records, story.getTotal() + news.getTotal());
    }

    @Override
    public Resp find4device(MediaQueryParams params) {
        var query = new OhMyQueryWrapper<MediaInfo>()
                .eq("m.category", params.getCategory())
                .eq("m.is_deleted", false);
        var page = mediaMapper.findPage(params.toPage(), query);

        return Resp.from(page);
    }

    @Override
    public Resp findPage(MediaQueryParams params) {
        var query = new OhMyLambdaQueryWrapper<Media>()
                .eq(Media::getCategory, params.getCategory())
                .select(Media::getId, Media::getTitle, Media::getCoverId, Media::getAssetId, Media::getCategory, Media::getCreatedAt)
                .orderByDesc(Media::getId);
        var page = mediaMapper.selectPage(params.toPage(), query);

        var records = page.getRecords()
                .stream()
                .map(it -> it)
                .toList();

        return Resp.of(records, page.getTotal());
    }

    @Override
    public Either<BizError, ?> create(MediaCreateParams params) {
        var query = new OhMyLambdaQueryWrapper<Media>()
                .eq(Media::getTitle, params.getTitle());

        return Option.when(mediaMapper.selectCount(query) <= 0, () -> new Media()
                        .setTitle(params.getTitle())
                        .setCategory(params.getCategory())
                        .setCoverId(params.getCoverId())
                        .setAssetId(params.getAssetId()))
                .toEither(BizError.UserNotExists)
                .map(it -> {
                    mediaMapper.insert(it);
                    return it.getId();
                });
    }

    @Override
    public Either<BizError, ?> delete(Integer id) {
        var query = new OhMyLambdaQueryWrapper<Media>()
                .eq(Media::getId, id)
                .select(Media::getId);

        return Option.of(mediaMapper.selectOne(query))
                .toEither(BizError.ResourceNotFound)
                .map(it -> {
                    mediaMapper.deleteById(id);
                    return true;
                });
    }

    @Override
    public Either<BizError, ?> generate(MediaGenerationParams params) {
        var query = new OhMyLambdaQueryWrapper<Media>()
                .eq(Media::getTitle, params.getTitle())
                .eq(Media::getCategory, params.getCategory());

        return Option.when(mediaMapper.selectCount(query) <= 0, () -> params)
                .toTry()
                .flatMap(_ -> CozeUtil.genReadingContent(params.getTitle(), params.getCategory()))
                .map(it -> {
                    var voice = params.getCategory().equals("news") ? "zh-CN-YunxiaNeural" : "zh-CN-XiaoyiNeural";
                    var title = params.getCategory().equals("news") ? STR."\{formatter.format(LocalDate.now())}资讯" : params.getTitle();

                    var media = new Media()
                            .setTitle(title)
                            .setCategory(params.getCategory());

                    AzureSpeechUtil.speakSsml(it.story(), voice, "zh-CN")
                            .flatMap(monoFilepath -> Try.of(() -> AudioUtils.monoToStereo(monoFilepath)))
                            .flatMap(stereoFilepath -> Try.of(() -> new FileInputStream(stereoFilepath)))
                            .flatMap(is -> fileResourceService.upload(params.getCategory(), STR."\{title}.mp3", is).toTry())
                            .peek(media::setAssetId);

                    ImageUtil.generate(it.cover(), "512*512")
                            .flatMap(url -> Try.of(() -> URI.create(url).toURL().openStream()))
                            .flatMap(is -> ImageUtil.png2jpg(is, 0.8f))
                            .flatMap(is -> fileResourceService.upload(params.getCategory(), STR."\{title}.jpg", is).toTry())
                            .peek(media::setCoverId);

                    return media.getId();
                })
                .toEither(BizError.SystemError);
    }
}
