package com.xiaozhi.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xiaozhi.common.web.AuthorizedUser;
import com.xiaozhi.common.web.BizError;
import com.xiaozhi.common.web.OhMyLambdaQueryWrapper;
import com.xiaozhi.dao.DeviceMapper;
import com.xiaozhi.dao.ManagerMapper;
import com.xiaozhi.dao.TaskMetaMapper;
import com.xiaozhi.dao.UserMapper;
import com.xiaozhi.entity.Manager;
import com.xiaozhi.entity.SysUser;
import com.xiaozhi.entity.TaskMeta;
import com.xiaozhi.enums.AuthType;
import com.xiaozhi.enums.TaskContent;
import com.xiaozhi.enums.TaskType;
import com.xiaozhi.enums.UserStatus;
import com.xiaozhi.service.AuthService;
import com.xiaozhi.service.WechatService;
import com.xiaozhi.utils.CozeUtil;
import com.xiaozhi.utils.DateUtils;
import com.xiaozhi.utils.HttpUtil;
import com.xiaozhi.utils.JwtUtil;
import com.xiaozhi.vo.ManagerLoginParams;
import com.xiaozhi.vo.UserLoginParams;
import io.vavr.control.Either;
import io.vavr.control.Option;
import io.vavr.control.Try;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class AuthServiceImpl implements AuthService {

    @Value("${jwt.secret}")
    private String jwtSecret;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private BCryptPasswordEncoder passwordEncoder;

    @Resource
    private WechatService wechatService;

    @Resource
    private ManagerMapper managerMapper;

    @Resource
    private DeviceMapper deviceMapper;

    @Resource
    private TaskMetaMapper taskMetaMapper;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    private final Map<String, String> DeviceMap = new HashMap<>() {{
        put("16710245700", "74:4d:bd:7f:2f:98");
    }};

    @Override
    public Either<BizError, ?> login(UserLoginParams params) {
        var query = new LambdaQueryWrapper<SysUser>()
                .eq(SysUser::getUsername, params.getUsername())
                .select(SysUser::getId, SysUser::getPassword, SysUser::getStatus);

        return Option.of(userMapper.selectOne(query))
                .toEither(BizError.UserNotExists)
                .filterOrElse(user -> user.getStatus() == UserStatus.Normal, _ -> BizError.UserFrozen)
                .filterOrElse(user -> passwordEncoder.matches(params.getPassword(), user.getPassword()), _ -> BizError.UserPasswordIncorrect)
                .map(user -> JwtUtil.authorize(user.getId(), AuthType.Admin, jwtSecret));
    }

    @Override
    public Either<BizError, AuthorizedUser> verify(String token) {
        return JwtUtil.verify(token, jwtSecret)
                .flatMap(authorizedUser -> {
                    var query = new LambdaQueryWrapper<SysUser>()
                            .eq(SysUser::getId, authorizedUser.getId())
                            .select(SysUser::getId, SysUser::getStatus);
                    return Option.of(userMapper.selectOne(query))
                            .toEither(BizError.UserNotExists)
                            .filterOrElse(user -> user.getStatus() == UserStatus.Normal, _ -> BizError.UserFrozen)
                            .map(_ -> authorizedUser);
                });
    }

    @Override
    public Either<BizError, ?> loginManager(ManagerLoginParams params) {
        return wechatService.getUserMobile(params.getCode())
                .toEither(BizError.BadRequest)
                .filterOrElse(DeviceMap::containsKey, _ -> BizError.UserFrozen)
                .map(mobile -> {
                    var query = new OhMyLambdaQueryWrapper<Manager>()
                            .eq(Manager::getMobile, mobile)
                            .select(Manager::getId, Manager::getDeviceId);
                    var manager = managerMapper.selectOne(query);

                    if (manager == null) {
                        manager = new Manager()
                                .setMobile(mobile)
                                .setDeviceId(DeviceMap.get(mobile));
                        managerMapper.insert(manager);
                    }

                    return JwtUtil.authorize(manager.getId(), AuthType.Manager, jwtSecret);
                });
    }

    @Override
    public Either<BizError, ?> loginManagerSms(ManagerLoginParams params) {
        var key = STR."xiaozhi:sms:\{params.getMobile()}:code";
        return Option.of(stringRedisTemplate.opsForValue().get(key))
                .toEither(BizError.ResourceNotFound)
                .filterOrElse(code -> code.equals(params.getCode()), _ -> BizError.UserPasswordIncorrect)
                .map(_ -> {
                    var query = new OhMyLambdaQueryWrapper<Manager>()
                            .eq(Manager::getMobile, params.getMobile())
                            .select(Manager::getId, Manager::getDeviceId);
                    var manager = managerMapper.selectOne(query);

                    if (manager == null) {
                        manager = new Manager()
                                .setMobile(params.getMobile())
                                .setDeviceId(DeviceMap.get(params.getMobile()))
                                .setStarterConvId(CozeUtil.getStarterConvId());
                        managerMapper.insert(manager);

                        // create default task config
                        initDefaultTask(manager.getId());
                    }

                    stringRedisTemplate.delete(key);

                    return JwtUtil.authorize(manager.getId(), AuthType.Manager, jwtSecret);
                });
    }

    @Override
    public Either<BizError, AuthorizedUser> verifyManager(String token) {
        return JwtUtil.verify(token, jwtSecret)
                .flatMap(authorizedUser -> {
                    var query = new LambdaQueryWrapper<Manager>()
                            .eq(Manager::getId, authorizedUser.getId())
                            .select(Manager::getId, Manager::getStatus, Manager::getDeviceId);
                    return Option.of(managerMapper.selectOne(query))
                            .toEither(BizError.UserNotExists)
                            .filterOrElse(user -> user.getStatus() == UserStatus.Normal, _ -> BizError.UserFrozen)
                            .map(manager -> {
                                // var deviceQuery = new OhMyLambdaQueryWrapper<SysDevice>()
                                //         .eq(SysDevice::getManagerId, manager.getId())
                                //         .select(SysDevice::getId, SysDevice::getDeviceId);
                                // var device = deviceMapper.selectOne(deviceQuery);
                                // if (device != null) {
                                //     authorizedUser.setDeviceId(device.getDeviceId());
                                // }

                                authorizedUser.setDeviceId(manager.getDeviceId());

                                return authorizedUser;
                            });
                });
    }

    @Override
    public Either<BizError, AuthorizedUser> verifyDevice(String deviceId) {
        var query = new OhMyLambdaQueryWrapper<Manager>()
                .eq(Manager::getDeviceId, deviceId)
                .select(Manager::getId);

        return Option.of(managerMapper.selectOne(query))
                .toEither(BizError.ResourceNotFound)
                .map(it -> new AuthorizedUser().setId(it.getId()).setType(AuthType.Device).setDeviceId(deviceId));
    }

    @Override
    public Either<BizError, ?> sendSmsCode(String mobile) {
        var key = STR."xiaozhi:sms:\{mobile}:code";

        var isOk = stringRedisTemplate.opsForValue().setIfAbsent(STR."xiaozhi:sms:\{mobile}:delay", "1", Duration.ofSeconds(60));

        return Option.when(Boolean.TRUE.equals(isOk), () -> RandomStringUtils.randomNumeric(6))
                .toEither(BizError.ResourceNotFound)
                .map(code -> {
                    stringRedisTemplate.opsForValue().set(key, code, Duration.ofSeconds(5 * 60));
                    // do send sms
                    sendSmsMsg(mobile, code);
                    return true;
                });
    }

    private void initDefaultTask(Integer managerId) {
        var tasks = List.of(
                new TaskMeta()
                        .setType(TaskType.Bedtime)
                        .setContent(TaskContent.Speaking.getValue() | TaskContent.Listening.getValue())
                        .setDuration("1020")
                        .setWeekMask(127)
                        .setTime("21:30")
                        .setIsEnabled(false)
                        .setManagerId(managerId)
                        .setCronExpr(DateUtils.buildCron("21:30", 127)),
                new TaskMeta()
                        .setType(TaskType.Listening)
                        .setContent(TaskContent.Listening.getValue())
                        .setDuration("20")
                        .setWeekMask(127)
                        .setTime("21:30")
                        .setIsEnabled(false)
                        .setManagerId(managerId)
                        .setCronExpr(DateUtils.buildCron("21:30", 127)),
                new TaskMeta()
                        .setType(TaskType.Information)
                        .setContent(TaskContent.Information.getValue())
                        .setDuration("0")
                        .setWeekMask(127)
                        .setTime("07:30")
                        .setIsEnabled(true)
                        .setManagerId(managerId)
                        .setCronExpr(DateUtils.buildCron("07:30", 127)),
                new TaskMeta()
                        .setType(TaskType.Conversation)
                        .setContent(TaskContent.Speaking.getValue())
                        .setDuration("15")
                        .setWeekMask(127)
                        .setTime("19:00")
                        .setIsEnabled(true)
                        .setManagerId(managerId)
                        .setCronExpr(DateUtils.buildCron("19:00", 127))
        );

        tasks.forEach(taskMetaMapper::insert);
    }

    private Try<?> sendSmsMsg(String mobile, String code) {
        return HttpUtil.post(
                "http://suona:8000/api/sms/tpl-sms/70123374",
                new SmsParams(List.of(mobile), List.of(code)),
                SmsResult.class
        ).filter(it -> it.code == 0);
    }

    record SmsParams(List<String> numbers, List<String> data) {
    }

    record SmsResult(int code, String msg) {
    }
}
