package com.xiaozhi.service;

import com.xiaozhi.common.web.BizError;
import com.xiaozhi.entity.Manager;
import com.xiaozhi.vo.ManagerUpdateParams;
import io.vavr.control.Either;
import org.jetbrains.annotations.NotNull;

public interface ManagerService {
    Either<BizError, Manager> detail(Integer id);
    Either<BizError, ?> update(Integer id, ManagerUpdateParams form);

    Manager findBy(String deviceId);
}
