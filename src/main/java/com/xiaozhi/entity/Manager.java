package com.xiaozhi.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.xiaozhi.enums.Gender;
import com.xiaozhi.enums.UserStatus;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.time.Period;

@Data
@Accessors(chain = true)
@TableName("managers")
@EqualsAndHashCode(callSuper = true)
public class Manager extends BaseEntity {

    private String name;
    private Gender gender;
    private LocalDate birth;
    private String cefr;
    private String mobile;
    private String deviceId;
    private UserStatus status;
    private String starterConvId;

    public int getAge() {
        return Period.between(birth, LocalDate.now()).getYears();
    }
}
