* AI 英语
** TODO 首屏 [4/7]
- [X] 眼睛根据不同的状态触发不同的动画
- [X] 眼睛页面轻触屏幕显示状态栏
- [ ] 左右滑动进入菜单页面 （滑动或长按进入滑动）
- [X] 唤醒时、说话时 先触发提示音再播放内容
- [X] 聆听中点击返回待机状态、说话中点击中断说话返回聆听中
- [ ] 非首屏唤起聊天时，通过浮层展现对话内容 (浮层可能问题；替代方案 返回首屏）
- [ ] 对话中通过字幕按钮切换眼睛表情和聊天记录（实现问题）

** TODO 设置 [4/4]
- [X] 声音设置
- [X] 睡眠模式
- [X] 设备信息
- [X] 系统信息

** TODO 听力内容 [2/4]
- [ ] 播放列表: 可根据分类展示
- [X] 基础播放控制: 播放、暂停、切歌
- [X] 全局播放控制: 播放任意资源时出现 (暂停 1 分钟后消失)
- [ ] 流媒体资源


#+BEGIN_SRC restclient
播放列表接口
GET https://rdmytikasapitest.lezhilong.cn/xiaozhi-esp32-server/api/v1/d/medias
Authorization: 74:4d:bd:7f:2f:98

#
{
  "data": [
    {
      "title": "三只老虎", # 音频名称
      "cover": "http://domain.com/tiger.png", # 封面图
      "url": "http://domain.com/tiger.wav" # 音频地址
    }
  ]
}
#+END_SRC

** TODO 待评估 [0/6]
- [ ] 播放器：采用本地播放，通过定时拉取音频并保存到SD卡中
- [ ] 支持动画表情与聊天记录切换
- [ ] 在非对话页面唤醒时跳转到对话页面
- [ ] 触摸相关移植
- [ ] 全局播放控制
- [ ] 音频歌词显示（将歌词嵌入音频中）

** IN-PROGRESS API [7/13]
- [X] 定时生成 task instance
- [X] 开启、关闭 meta 时，处理 instance；同一时刻不能有多个任务
- [X] 执行 task
- [X] 计算 task content 执行时间，切换任务内容
- [X] 主动推送测试
- [X] 口语任务的内容存入聊天记录
- [X] 家长端主动推送的内容存入聊天记录
- [ ] Summary测试
- [ ] 更新 TaskInstance 完成状态
- [ ] Azure 替换
- [ ] 流式 TTS
- [ ] intent 优化
- [ ] 稳定性优化

** IN-PROGRESS rk [0/6]
- [ ] ling 是如何实现的
- [ ] 安卓开发流程： 直接开发 APP，然后板子上测试？
- [ ] APP 能否跟 硬件并行
- [ ] 开发板选型： 手机芯片 vs rk
- [ ] 定制硬件：麦克风阵列、喇叭, 能否独立完成 或 接入开发
- [ ] 学习机建议： 起步最快，成熟稳定
